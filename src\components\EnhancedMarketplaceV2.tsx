import React, { useState, useEffect } from 'react';
import { Search, Filter, MapPin, Heart, MessageCircle, Share, Star, DollarSign, Calendar, User, Eye, Bookmark, ShoppingCart, Package, Truck, Shield } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { storage } from '@/lib/storage';
import { getSafeImage } from '@/lib/constants';

interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  images: string[];
  location: string;
  seller: {
    id: string;
    name: string;
    avatar: string;
    rating: number;
    verified: boolean;
    responseTime: string;
  };
  createdAt: string;
  views: number;
  likes: number;
  isLiked: boolean;
  isSaved: boolean;
  tags: string[];
  shipping: {
    available: boolean;
    cost: number;
    methods: string[];
  };
  negotiable: boolean;
  sold: boolean;
}

interface EnhancedMarketplaceV2Props {
  className?: string;
}

const EnhancedMarketplaceV2: React.FC<EnhancedMarketplaceV2Props> = ({ className }) => {
  const [items, setItems] = useState<MarketplaceItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MarketplaceItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [selectedCondition, setSelectedCondition] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedItem, setSelectedItem] = useState<MarketplaceItem | null>(null);
  const [savedItems, setSavedItems] = useState<string[]>([]);

  const categories = [
    { id: 'all', name: 'All Categories', icon: '🏪' },
    { id: 'electronics', name: 'Electronics', icon: '📱' },
    { id: 'vehicles', name: 'Vehicles', icon: '🚗' },
    { id: 'home', name: 'Home & Garden', icon: '🏠' },
    { id: 'clothing', name: 'Clothing', icon: '👕' },
    { id: 'sports', name: 'Sports & Recreation', icon: '⚽' },
    { id: 'books', name: 'Books & Media', icon: '📚' },
    { id: 'toys', name: 'Toys & Games', icon: '🎮' },
    { id: 'furniture', name: 'Furniture', icon: '🪑' },
    { id: 'services', name: 'Services', icon: '🔧' }
  ];

  const conditions = [
    { id: 'all', name: 'All Conditions' },
    { id: 'new', name: 'New' },
    { id: 'like_new', name: 'Like New' },
    { id: 'good', name: 'Good' },
    { id: 'fair', name: 'Fair' },
    { id: 'poor', name: 'Poor' }
  ];

  // Mock marketplace data
  const mockItems: MarketplaceItem[] = [
    {
      id: 'item_1',
      title: 'iPhone 14 Pro Max - 256GB',
      description: 'Excellent condition iPhone 14 Pro Max in Space Black. Includes original box, charger, and screen protector already applied.',
      price: 899,
      currency: 'USD',
      category: 'electronics',
      condition: 'like_new',
      images: [getSafeImage('POSTS', 0), getSafeImage('POSTS', 1)],
      location: 'New York, NY',
      seller: {
        id: 'seller_1',
        name: 'John Smith',
        avatar: getSafeImage('AVATARS', 0),
        rating: 4.8,
        verified: true,
        responseTime: 'Usually responds within an hour'
      },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      views: 245,
      likes: 18,
      isLiked: false,
      isSaved: false,
      tags: ['smartphone', 'apple', 'unlocked'],
      shipping: {
        available: true,
        cost: 15,
        methods: ['Standard', 'Express']
      },
      negotiable: true,
      sold: false
    },
    {
      id: 'item_2',
      title: '2019 Honda Civic - Low Mileage',
      description: 'Well-maintained Honda Civic with only 35,000 miles. Regular oil changes, non-smoker, garage kept.',
      price: 18500,
      currency: 'USD',
      category: 'vehicles',
      condition: 'good',
      images: [getSafeImage('POSTS', 2), getSafeImage('POSTS', 3)],
      location: 'Los Angeles, CA',
      seller: {
        id: 'seller_2',
        name: 'Sarah Johnson',
        avatar: getSafeImage('AVATARS', 1),
        rating: 4.9,
        verified: true,
        responseTime: 'Usually responds within 2 hours'
      },
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      views: 892,
      likes: 45,
      isLiked: true,
      isSaved: true,
      tags: ['honda', 'sedan', 'fuel-efficient'],
      shipping: {
        available: false,
        cost: 0,
        methods: []
      },
      negotiable: true,
      sold: false
    },
    {
      id: 'item_3',
      title: 'Modern Sectional Sofa',
      description: 'Beautiful gray sectional sofa, perfect for modern living rooms. Very comfortable and in excellent condition.',
      price: 650,
      currency: 'USD',
      category: 'furniture',
      condition: 'good',
      images: [getSafeImage('POSTS', 4), getSafeImage('POSTS', 5)],
      location: 'Chicago, IL',
      seller: {
        id: 'seller_3',
        name: 'Mike Wilson',
        avatar: getSafeImage('AVATARS', 2),
        rating: 4.6,
        verified: false,
        responseTime: 'Usually responds within a day'
      },
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      views: 156,
      likes: 12,
      isLiked: false,
      isSaved: false,
      tags: ['furniture', 'sectional', 'modern'],
      shipping: {
        available: true,
        cost: 75,
        methods: ['White Glove Delivery']
      },
      negotiable: true,
      sold: false
    },
    {
      id: 'item_4',
      title: 'Gaming Laptop - RTX 3070',
      description: 'High-performance gaming laptop with RTX 3070, 16GB RAM, 1TB SSD. Perfect for gaming and content creation.',
      price: 1299,
      currency: 'USD',
      category: 'electronics',
      condition: 'like_new',
      images: [getSafeImage('POSTS', 0)],
      location: 'Austin, TX',
      seller: {
        id: 'seller_4',
        name: 'Alex Chen',
        avatar: getSafeImage('AVATARS', 3),
        rating: 4.7,
        verified: true,
        responseTime: 'Usually responds within 30 minutes'
      },
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      views: 324,
      likes: 28,
      isLiked: false,
      isSaved: true,
      tags: ['gaming', 'laptop', 'rtx'],
      shipping: {
        available: true,
        cost: 25,
        methods: ['Standard', 'Express', 'Overnight']
      },
      negotiable: false,
      sold: false
    }
  ];

  // Load data on component mount
  useEffect(() => {
    const saved = storage.get<string[]>('saved_marketplace_items', []);
    setSavedItems(saved);
    
    const itemsWithSavedStatus = mockItems.map(item => ({
      ...item,
      isSaved: saved.includes(item.id)
    }));
    
    setItems(itemsWithSavedStatus);
    setFilteredItems(itemsWithSavedStatus);
  }, []);

  // Filter and sort items
  useEffect(() => {
    let filtered = items.filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
      const matchesPrice = item.price >= priceRange[0] && item.price <= priceRange[1];
      const matchesCondition = selectedCondition === 'all' || item.condition === selectedCondition;
      const notSold = !item.sold;
      
      return matchesSearch && matchesCategory && matchesPrice && matchesCondition && notSold;
    });

    // Sort items
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'price_low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price_high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'popular':
        filtered.sort((a, b) => (b.views + b.likes) - (a.views + a.likes));
        break;
    }

    setFilteredItems(filtered);
  }, [items, searchQuery, selectedCategory, priceRange, selectedCondition, sortBy]);

  const handleLike = (itemId: string) => {
    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        return {
          ...item,
          isLiked: !item.isLiked,
          likes: item.isLiked ? item.likes - 1 : item.likes + 1
        };
      }
      return item;
    }));
  };

  const handleSave = (itemId: string) => {
    const updatedSaved = savedItems.includes(itemId)
      ? savedItems.filter(id => id !== itemId)
      : [...savedItems, itemId];
    
    setSavedItems(updatedSaved);
    storage.set('saved_marketplace_items', updatedSaved);
    
    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        return { ...item, isSaved: !item.isSaved };
      }
      return item;
    }));
    
    toast.success(updatedSaved.includes(itemId) ? 'Item saved!' : 'Item removed from saved');
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'new': return 'bg-green-100 text-green-800';
      case 'like_new': return 'bg-blue-100 text-blue-800';
      case 'good': return 'bg-yellow-100 text-yellow-800';
      case 'fair': return 'bg-orange-100 text-orange-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Marketplace</h2>
          <p className="text-gray-600 dark:text-gray-400">{filteredItems.length} items available</p>
        </div>
        <Button onClick={() => setShowFilters(true)} variant="outline" className="flex items-center gap-2">
          <Filter className="w-4 h-4" />
          Filters
        </Button>
      </div>

      {/* Search and Quick Filters */}
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search marketplace..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          {categories.slice(0, 6).map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex items-center gap-1"
            >
              <span>{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>
        
        <div className="flex items-center gap-4">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="oldest">Oldest First</SelectItem>
              <SelectItem value="price_low">Price: Low to High</SelectItem>
              <SelectItem value="price_high">Price: High to Low</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredItems.map((item) => (
          <Card key={item.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setSelectedItem(item)}>
            <div className="relative">
              <img
                src={item.images[0]}
                alt={item.title}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <div className="absolute top-2 left-2">
                <Badge className={getConditionColor(item.condition)}>
                  {item.condition.replace('_', ' ')}
                </Badge>
              </div>
              <div className="absolute top-2 right-2 flex gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-white/80 hover:bg-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSave(item.id);
                  }}
                >
                  <Bookmark className={`w-4 h-4 ${item.isSaved ? 'fill-current text-blue-500' : ''}`} />
                </Button>
              </div>
              {item.negotiable && (
                <div className="absolute bottom-2 left-2">
                  <Badge variant="secondary" className="text-xs">
                    Negotiable
                  </Badge>
                </div>
              )}
            </div>
            
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-1 line-clamp-1">{item.title}</h3>
              <p className="text-2xl font-bold text-green-600 mb-2">
                {formatPrice(item.price, item.currency)}
              </p>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                {item.description}
              </p>
              
              <div className="flex items-center gap-2 mb-3">
                <Avatar className="w-6 h-6">
                  <AvatarImage src={item.seller.avatar} />
                  <AvatarFallback>{item.seller.name[0]}</AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium">{item.seller.name}</span>
                {item.seller.verified && (
                  <Shield className="w-3 h-3 text-blue-500" />
                )}
                <div className="flex items-center gap-1 ml-auto">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span className="text-xs">{item.seller.rating}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3" />
                  {item.location}
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {item.views}
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className={`w-3 h-3 ${item.isLiked ? 'fill-current text-red-500' : ''}`} />
                    {item.likes}
                  </div>
                </div>
              </div>
              
              {item.shipping.available && (
                <div className="flex items-center gap-1 mt-2 text-xs text-green-600">
                  <Truck className="w-3 h-3" />
                  Shipping available
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <div className="text-center py-12">
          <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No items found matching your criteria</p>
        </div>
      )}

      {/* Filters Dialog */}
      <Dialog open={showFilters} onOpenChange={setShowFilters}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Filter Items</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            <div>
              <label className="text-sm font-medium mb-3 block">Category</label>
              <div className="grid grid-cols-2 gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="justify-start"
                  >
                    <span className="mr-2">{category.icon}</span>
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-3 block">
                Price Range: {formatPrice(priceRange[0], 'USD')} - {formatPrice(priceRange[1], 'USD')}
              </label>
              <Slider
                value={priceRange}
                onValueChange={setPriceRange}
                max={5000}
                min={0}
                step={50}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-3 block">Condition</label>
              <Select value={selectedCondition} onValueChange={setSelectedCondition}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {conditions.map((condition) => (
                    <SelectItem key={condition.id} value={condition.id}>
                      {condition.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex gap-2">
              <Button onClick={() => setShowFilters(false)} className="flex-1">
                Apply Filters
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setSelectedCategory('all');
                  setPriceRange([0, 1000]);
                  setSelectedCondition('all');
                }}
              >
                Reset
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Item Detail Dialog */}
      <Dialog open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedItem && (
            <>
              <DialogHeader>
                <DialogTitle className="text-xl">{selectedItem.title}</DialogTitle>
              </DialogHeader>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <img
                    src={selectedItem.images[0]}
                    alt={selectedItem.title}
                    className="w-full h-64 object-cover rounded-lg mb-4"
                  />
                  {selectedItem.images.length > 1 && (
                    <div className="grid grid-cols-4 gap-2">
                      {selectedItem.images.slice(1).map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`${selectedItem.title} ${index + 2}`}
                          className="w-full h-16 object-cover rounded"
                        />
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="space-y-4">
                  <div>
                    <p className="text-3xl font-bold text-green-600 mb-2">
                      {formatPrice(selectedItem.price, selectedItem.currency)}
                      {selectedItem.negotiable && (
                        <Badge variant="secondary" className="ml-2">Negotiable</Badge>
                      )}
                    </p>
                    <Badge className={getConditionColor(selectedItem.condition)}>
                      {selectedItem.condition.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <p className="text-gray-700 dark:text-gray-300">
                    {selectedItem.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-1">
                    {selectedItem.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Seller Information</h4>
                    <div className="flex items-center gap-3 mb-2">
                      <Avatar>
                        <AvatarImage src={selectedItem.seller.avatar} />
                        <AvatarFallback>{selectedItem.seller.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{selectedItem.seller.name}</span>
                          {selectedItem.seller.verified && (
                            <Shield className="w-4 h-4 text-blue-500" />
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                          {selectedItem.seller.rating} rating
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{selectedItem.seller.responseTime}</p>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Location & Shipping</h4>
                    <div className="flex items-center gap-1 mb-2">
                      <MapPin className="w-4 h-4" />
                      {selectedItem.location}
                    </div>
                    {selectedItem.shipping.available ? (
                      <div className="text-sm text-green-600">
                        <div className="flex items-center gap-1">
                          <Truck className="w-4 h-4" />
                          Shipping available for {formatPrice(selectedItem.shipping.cost, selectedItem.currency)}
                        </div>
                        <p className="text-xs mt-1">
                          Methods: {selectedItem.shipping.methods.join(', ')}
                        </p>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Pickup only</p>
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <Button className="flex-1">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Message Seller
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => handleLike(selectedItem.id)}
                      className={selectedItem.isLiked ? 'text-red-500' : ''}
                    >
                      <Heart className={`w-4 h-4 ${selectedItem.isLiked ? 'fill-current' : ''}`} />
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => handleSave(selectedItem.id)}
                    >
                      <Bookmark className={`w-4 h-4 ${selectedItem.isSaved ? 'fill-current text-blue-500' : ''}`} />
                    </Button>
                    <Button variant="outline">
                      <Share className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedMarketplaceV2;