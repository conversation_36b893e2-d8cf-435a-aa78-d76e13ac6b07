import React, { useState, useEffect } from 'react';
import { MapPin, Search, Navigation, Star, Clock, Users, Camera, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import { storage } from '@/lib/storage';
import { getSafeImage } from '@/lib/constants';

interface Location {
  id: string;
  name: string;
  address: string;
  category: string;
  rating?: number;
  distance?: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
  checkins?: number;
  photos?: string[];
}

interface CheckIn {
  id: string;
  location: Location;
  timestamp: string;
  message?: string;
  privacy: 'public' | 'friends' | 'only_me';
  companions?: string[];
  photos?: string[];
  author: {
    name: string;
    avatar: string;
  };
}

interface LocationCheckInProps {
  isOpen: boolean;
  onClose: () => void;
  onCheckIn?: (checkIn: CheckIn) => void;
}

const LocationCheckIn: React.FC<LocationCheckInProps> = ({ isOpen, onClose, onCheckIn }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [message, setMessage] = useState('');
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'only_me'>('friends');
  const [nearbyPlaces, setNearbyPlaces] = useState<Location[]>([]);
  const [recentCheckIns, setRecentCheckIns] = useState<CheckIn[]>([]);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Mock nearby places data
  const mockNearbyPlaces: Location[] = [
    {
      id: 'place_1',
      name: 'Central Park',
      address: '5th Ave, New York, NY 10028',
      category: 'Park',
      rating: 4.8,
      distance: '0.2 km',
      checkins: 1250,
      coordinates: { lat: 40.7829, lng: -73.9654 },
      photos: [getSafeImage('POSTS', 0)]
    },
    {
      id: 'place_2',
      name: 'Starbucks Coffee',
      address: '123 Main St, New York, NY 10001',
      category: 'Coffee Shop',
      rating: 4.2,
      distance: '0.5 km',
      checkins: 890,
      coordinates: { lat: 40.7580, lng: -73.9855 },
      photos: [getSafeImage('POSTS', 1)]
    },
    {
      id: 'place_3',
      name: 'Times Square',
      address: 'Times Square, New York, NY 10036',
      category: 'Tourist Attraction',
      rating: 4.5,
      distance: '1.2 km',
      checkins: 5670,
      coordinates: { lat: 40.7580, lng: -73.9855 },
      photos: [getSafeImage('POSTS', 2)]
    },
    {
      id: 'place_4',
      name: 'Brooklyn Bridge',
      address: 'Brooklyn Bridge, New York, NY 10038',
      category: 'Landmark',
      rating: 4.9,
      distance: '2.1 km',
      checkins: 3420,
      coordinates: { lat: 40.7061, lng: -73.9969 },
      photos: [getSafeImage('POSTS', 3)]
    },
    {
      id: 'place_5',
      name: 'The Metropolitan Museum',
      address: '1000 5th Ave, New York, NY 10028',
      category: 'Museum',
      rating: 4.7,
      distance: '1.8 km',
      checkins: 2100,
      coordinates: { lat: 40.7794, lng: -73.9632 },
      photos: [getSafeImage('POSTS', 4)]
    }
  ];

  // Load recent check-ins from storage
  useEffect(() => {
    const saved = storage.get<CheckIn[]>('recent_checkins', []);
    setRecentCheckIns(saved);
  }, []);

  // Initialize nearby places
  useEffect(() => {
    if (isOpen) {
      setNearbyPlaces(mockNearbyPlaces);
    }
  }, [isOpen]);

  // Get user's current location
  const getCurrentLocation = () => {
    setIsLoadingLocation(true);
    
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setUserLocation({ lat: latitude, lng: longitude });
          toast.success('Location detected successfully!');
          setIsLoadingLocation(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          toast.error('Unable to get your location. Using default nearby places.');
          setIsLoadingLocation(false);
        }
      );
    } else {
      toast.error('Geolocation is not supported by this browser.');
      setIsLoadingLocation(false);
    }
  };

  // Filter places based on search query
  const filteredPlaces = nearbyPlaces.filter(place =>
    place.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    place.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
    place.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleCheckIn = () => {
    if (!selectedLocation) {
      toast.error('Please select a location to check in');
      return;
    }

    const checkIn: CheckIn = {
      id: `checkin_${Date.now()}`,
      location: selectedLocation,
      timestamp: new Date().toISOString(),
      message: message.trim() || undefined,
      privacy,
      author: {
        name: 'You',
        avatar: getSafeImage('AVATARS', 0)
      }
    };

    // Save to recent check-ins
    const updatedCheckIns = [checkIn, ...recentCheckIns.slice(0, 9)];
    setRecentCheckIns(updatedCheckIns);
    storage.set('recent_checkins', updatedCheckIns);

    // Call parent callback
    onCheckIn?.(checkIn);

    // Reset form
    setSelectedLocation(null);
    setMessage('');
    setSearchQuery('');
    
    toast.success(`Checked in at ${selectedLocation.name}!`);
    onClose();
  };

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case 'restaurant':
      case 'coffee shop':
        return '🍽️';
      case 'park':
        return '🌳';
      case 'museum':
        return '🏛️';
      case 'landmark':
        return '🏛️';
      case 'tourist attraction':
        return '🎯';
      case 'shopping':
        return '🛍️';
      case 'hotel':
        return '🏨';
      default:
        return '📍';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Check In
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Location Detection */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={getCurrentLocation}
              disabled={isLoadingLocation}
              className="flex items-center gap-2"
            >
              <Navigation className="w-4 h-4" />
              {isLoadingLocation ? 'Detecting...' : 'Use Current Location'}
            </Button>
            {userLocation && (
              <Badge variant="secondary" className="text-green-600">
                Location detected
              </Badge>
            )}
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search for places..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selected Location */}
          {selectedLocation && (
            <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{getCategoryIcon(selectedLocation.category)}</div>
                    <div>
                      <h3 className="font-semibold">{selectedLocation.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedLocation.address}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="secondary">{selectedLocation.category}</Badge>
                        {selectedLocation.rating && (
                          <div className="flex items-center gap-1 text-sm">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            {selectedLocation.rating}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedLocation(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Places List */}
          <div className="space-y-2 max-h-60 overflow-y-auto">
            <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300">
              {searchQuery ? 'Search Results' : 'Nearby Places'}
            </h3>
            {filteredPlaces.map((place) => (
              <Card
                key={place.id}
                className={`cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 ${
                  selectedLocation?.id === place.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedLocation(place)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center gap-3">
                    <div className="text-xl">{getCategoryIcon(place.category)}</div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{place.name}</h4>
                        {selectedLocation?.id === place.id && (
                          <Check className="w-4 h-4 text-blue-500" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {place.address}
                      </p>
                      <div className="flex items-center gap-3 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {place.category}
                        </Badge>
                        {place.rating && (
                          <div className="flex items-center gap-1 text-xs">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            {place.rating}
                          </div>
                        )}
                        {place.distance && (
                          <span className="text-xs text-gray-500">{place.distance}</span>
                        )}
                        {place.checkins && (
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Users className="w-3 h-3" />
                            {place.checkins}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Message */}
          {selectedLocation && (
            <div>
              <label className="text-sm font-medium mb-2 block">Message (Optional)</label>
              <Input
                placeholder="Say something about this place..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
          )}

          {/* Privacy */}
          {selectedLocation && (
            <div>
              <label className="text-sm font-medium mb-2 block">Privacy</label>
              <div className="flex gap-2">
                <Button
                  variant={privacy === 'public' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPrivacy('public')}
                >
                  🌍 Public
                </Button>
                <Button
                  variant={privacy === 'friends' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPrivacy('friends')}
                >
                  👥 Friends
                </Button>
                <Button
                  variant={privacy === 'only_me' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setPrivacy('only_me')}
                >
                  🔒 Only Me
                </Button>
              </div>
            </div>
          )}

          {/* Recent Check-ins */}
          {recentCheckIns.length > 0 && !selectedLocation && (
            <div>
              <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">
                Recent Check-ins
              </h3>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {recentCheckIns.slice(0, 3).map((checkIn) => (
                  <Card
                    key={checkIn.id}
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                    onClick={() => setSelectedLocation(checkIn.location)}
                  >
                    <CardContent className="p-2">
                      <div className="flex items-center gap-2">
                        <Clock className="w-3 h-3 text-gray-400" />
                        <span className="text-sm font-medium">{checkIn.location.name}</span>
                        <span className="text-xs text-gray-500">
                          {new Date(checkIn.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4">
            <Button
              onClick={handleCheckIn}
              disabled={!selectedLocation}
              className="flex-1"
            >
              <MapPin className="w-4 h-4 mr-2" />
              Check In
            </Button>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LocationCheckIn;