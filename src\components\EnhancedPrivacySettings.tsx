import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Eye, 
  EyeOff, 
  Lock, 
  Globe, 
  Users, 
  UserCheck, 
  Bell, 
  MessageSquare, 
  Search,
  MapPin,
  Camera,
  Tag,
  Share2,
  Settings,
  Info,
  AlertTriangle,
  Check
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';

interface PrivacySettings {
  profile: {
    visibility: 'public' | 'friends' | 'private';
    searchable: boolean;
    showOnlineStatus: boolean;
    showLastSeen: boolean;
    allowFriendRequests: 'everyone' | 'friends-of-friends' | 'none';
  };
  posts: {
    defaultPrivacy: 'public' | 'friends' | 'custom';
    allowComments: 'everyone' | 'friends' | 'none';
    allowShares: boolean;
    allowTagging: 'everyone' | 'friends' | 'none';
    reviewTags: boolean;
  };
  messaging: {
    allowMessages: 'everyone' | 'friends' | 'none';
    allowGroupInvites: boolean;
    readReceipts: boolean;
    typingIndicators: boolean;
  };
  location: {
    shareLocation: boolean;
    allowLocationTagging: 'everyone' | 'friends' | 'none';
    showNearbyFriends: boolean;
  };
  notifications: {
    emailNotifications: boolean;
    pushNotifications: boolean;
    smsNotifications: boolean;
    marketingEmails: boolean;
  };
  dataAndPrivacy: {
    dataDownload: boolean;
    accountDeactivation: boolean;
    dataSharing: boolean;
    adPersonalization: boolean;
  };
}

interface EnhancedPrivacySettingsProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (settings: PrivacySettings) => void;
  initialSettings?: Partial<PrivacySettings>;
}

const EnhancedPrivacySettings: React.FC<EnhancedPrivacySettingsProps> = ({
  isOpen,
  onClose,
  onSave,
  initialSettings = {}
}) => {
  const [settings, setSettings] = useState<PrivacySettings>({
    profile: {
      visibility: 'friends',
      searchable: true,
      showOnlineStatus: true,
      showLastSeen: false,
      allowFriendRequests: 'friends-of-friends',
      ...initialSettings.profile
    },
    posts: {
      defaultPrivacy: 'friends',
      allowComments: 'friends',
      allowShares: true,
      allowTagging: 'friends',
      reviewTags: true,
      ...initialSettings.posts
    },
    messaging: {
      allowMessages: 'friends',
      allowGroupInvites: true,
      readReceipts: true,
      typingIndicators: true,
      ...initialSettings.messaging
    },
    location: {
      shareLocation: false,
      allowLocationTagging: 'friends',
      showNearbyFriends: false,
      ...initialSettings.location
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      marketingEmails: false,
      ...initialSettings.notifications
    },
    dataAndPrivacy: {
      dataDownload: true,
      accountDeactivation: true,
      dataSharing: false,
      adPersonalization: false,
      ...initialSettings.dataAndPrivacy
    }
  });

  const [activeTab, setActiveTab] = useState('profile');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setHasChanges(JSON.stringify(settings) !== JSON.stringify({
      profile: { visibility: 'friends', searchable: true, showOnlineStatus: true, showLastSeen: false, allowFriendRequests: 'friends-of-friends' },
      posts: { defaultPrivacy: 'friends', allowComments: 'friends', allowShares: true, allowTagging: 'friends', reviewTags: true },
      messaging: { allowMessages: 'friends', allowGroupInvites: true, readReceipts: true, typingIndicators: true },
      location: { shareLocation: false, allowLocationTagging: 'friends', showNearbyFriends: false },
      notifications: { emailNotifications: true, pushNotifications: true, smsNotifications: false, marketingEmails: false },
      dataAndPrivacy: { dataDownload: true, accountDeactivation: true, dataSharing: false, adPersonalization: false }
    }));
  }, [settings]);

  const updateSetting = (category: keyof PrivacySettings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  const handleSave = () => {
    onSave(settings);
    toast.success('Privacy settings updated successfully');
    onClose();
  };

  const resetToDefaults = () => {
    setSettings({
      profile: { visibility: 'friends', searchable: true, showOnlineStatus: true, showLastSeen: false, allowFriendRequests: 'friends-of-friends' },
      posts: { defaultPrivacy: 'friends', allowComments: 'friends', allowShares: true, allowTagging: 'friends', reviewTags: true },
      messaging: { allowMessages: 'friends', allowGroupInvites: true, readReceipts: true, typingIndicators: true },
      location: { shareLocation: false, allowLocationTagging: 'friends', showNearbyFriends: false },
      notifications: { emailNotifications: true, pushNotifications: true, smsNotifications: false, marketingEmails: false },
      dataAndPrivacy: { dataDownload: true, accountDeactivation: true, dataSharing: false, adPersonalization: false }
    });
    toast.info('Settings reset to defaults');
  };

  const getPrivacyLevel = () => {
    const publicCount = Object.values(settings).reduce((count, category) => {
      return count + Object.values(category).filter(value => 
        value === 'public' || value === 'everyone' || (typeof value === 'boolean' && value)
      ).length;
    }, 0);
    
    if (publicCount <= 5) return { level: 'High', color: 'text-green-600', icon: Shield };
    if (publicCount <= 10) return { level: 'Medium', color: 'text-yellow-600', icon: AlertTriangle };
    return { level: 'Low', color: 'text-red-600', icon: AlertTriangle };
  };

  const privacyLevel = getPrivacyLevel();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-6 h-6 text-blue-600" />
              <div>
                <CardTitle>Privacy & Security Settings</CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <privacyLevel.icon className={`w-4 h-4 ${privacyLevel.color}`} />
                  <span className={`text-sm ${privacyLevel.color}`}>
                    Privacy Level: {privacyLevel.level}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {hasChanges && (
                <Badge variant="secondary">Unsaved changes</Badge>
              )}
              <Button variant="ghost" onClick={onClose}>×</Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="overflow-y-auto max-h-[70vh]">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="posts">Posts</TabsTrigger>
              <TabsTrigger value="messaging">Messages</TabsTrigger>
              <TabsTrigger value="location">Location</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="data">Data & Privacy</TabsTrigger>
            </TabsList>

            {/* Profile Privacy */}
            <TabsContent value="profile" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Eye className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Profile Visibility</h3>
                      <p className="text-sm text-gray-500">Who can see your profile</p>
                    </div>
                  </div>
                  <Select 
                    value={settings.profile.visibility} 
                    onValueChange={(value: any) => updateSetting('profile', 'visibility', value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">
                        <div className="flex items-center space-x-2">
                          <Globe className="w-4 h-4" />
                          <span>Public</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="friends">
                        <div className="flex items-center space-x-2">
                          <Users className="w-4 h-4" />
                          <span>Friends</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="private">
                        <div className="flex items-center space-x-2">
                          <Lock className="w-4 h-4" />
                          <span>Private</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Search className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Searchable Profile</h3>
                      <p className="text-sm text-gray-500">Allow others to find you in search</p>
                    </div>
                  </div>
                  <Switch
                    checked={settings.profile.searchable}
                    onCheckedChange={(checked) => updateSetting('profile', 'searchable', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full" />
                    <div>
                      <h3 className="font-medium">Show Online Status</h3>
                      <p className="text-sm text-gray-500">Let friends see when you're online</p>
                    </div>
                  </div>
                  <Switch
                    checked={settings.profile.showOnlineStatus}
                    onCheckedChange={(checked) => updateSetting('profile', 'showOnlineStatus', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Eye className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Show Last Seen</h3>
                      <p className="text-sm text-gray-500">Show when you were last active</p>
                    </div>
                  </div>
                  <Switch
                    checked={settings.profile.showLastSeen}
                    onCheckedChange={(checked) => updateSetting('profile', 'showLastSeen', checked)}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <UserCheck className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Friend Requests</h3>
                      <p className="text-sm text-gray-500">Who can send you friend requests</p>
                    </div>
                  </div>
                  <Select 
                    value={settings.profile.allowFriendRequests} 
                    onValueChange={(value: any) => updateSetting('profile', 'allowFriendRequests', value)}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="everyone">Everyone</SelectItem>
                      <SelectItem value="friends-of-friends">Friends of friends</SelectItem>
                      <SelectItem value="none">No one</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>

            {/* Posts Privacy */}
            <TabsContent value="posts" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Share2 className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Default Post Privacy</h3>
                      <p className="text-sm text-gray-500">Who can see your posts by default</p>
                    </div>
                  </div>
                  <Select 
                    value={settings.posts.defaultPrivacy} 
                    onValueChange={(value: any) => updateSetting('posts', 'defaultPrivacy', value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">Public</SelectItem>
                      <SelectItem value="friends">Friends</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <MessageSquare className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Comments</h3>
                      <p className="text-sm text-gray-500">Who can comment on your posts</p>
                    </div>
                  </div>
                  <Select 
                    value={settings.posts.allowComments} 
                    onValueChange={(value: any) => updateSetting('posts', 'allowComments', value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="everyone">Everyone</SelectItem>
                      <SelectItem value="friends">Friends</SelectItem>
                      <SelectItem value="none">No one</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Share2 className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Allow Shares</h3>
                      <p className="text-sm text-gray-500">Let others share your posts</p>
                    </div>
                  </div>
                  <Switch
                    checked={settings.posts.allowShares}
                    onCheckedChange={(checked) => updateSetting('posts', 'allowShares', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Tag className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Tagging</h3>
                      <p className="text-sm text-gray-500">Who can tag you in posts</p>
                    </div>
                  </div>
                  <Select 
                    value={settings.posts.allowTagging} 
                    onValueChange={(value: any) => updateSetting('posts', 'allowTagging', value)}
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="everyone">Everyone</SelectItem>
                      <SelectItem value="friends">Friends</SelectItem>
                      <SelectItem value="none">No one</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Check className="w-5 h-5 text-gray-500" />
                    <div>
                      <h3 className="font-medium">Review Tags</h3>
                      <p className="text-sm text-gray-500">Review posts you're tagged in before they appear</p>
                    </div>
                  </div>
                  <Switch
                    checked={settings.posts.reviewTags}
                    onCheckedChange={(checked) => updateSetting('posts', 'reviewTags', checked)}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Add other tab contents here... */}
          </Tabs>
        </CardContent>

        <div className="p-6 border-t flex items-center justify-between">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={!hasChanges}>
              Save Changes
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedPrivacySettings;
