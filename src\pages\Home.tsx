import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PostCard from '@/components/posts/PostCard';
import Stories from '@/components/Stories';
import CreatePost from '@/components/posts/CreatePost';
import NewsFeedTabs from '@/components/NewsFeedTabs';
import NewsFeedFilters from '@/components/NewsFeedFilters';
import NewsFeedSkeleton from '@/components/NewsFeedSkeleton';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RefreshCw, Filter, ArrowUp } from 'lucide-react';
import { usePostManagement } from '@/hooks/usePostManagement';
import { useFilterManagement } from '@/hooks/useFilterManagement';

// Constants
const NEW_POSTS_CHECK_INTERVAL = 30000; // 30 seconds
const LOAD_MORE_THRESHOLD = 20;

const Home = () => {
  // Custom hooks for better separation of concerns
  const {
    posts,
    isLoading,
    isLoadingMore,
    error,
    fetchPosts,
    loadMorePosts,
    refreshPosts,
    createPost,
  } = usePostManagement();

  const {
    filters,
    showFilters,
    setShowFilters,
    handleApplyFilters,
    handleClearFilters,
    applyFiltersToPost,
    hasActiveFilters,
  } = useFilterManagement();
  
  // UI state
  const [activeTab, setActiveTab] = useState('foryou');
  const [sortBy, setSortBy] = useState('recent');
  const [hasNewPosts, setHasNewPosts] = useState(false);

  // Initial load
  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  // Handle refresh with new posts notification
  const handleRefresh = useCallback(async () => {
    setHasNewPosts(false);
    await refreshPosts();
  }, [refreshPosts]);

  // Handle tab change
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
  }, []);

  // Handle sort change
  const handleSortChange = useCallback((sort: string) => {
    setSortBy(sort);
  }, []);

  // Handle create post
  const handleCreatePost = useCallback((newPost: {
    content: string;
    image_url?: string;
    feeling?: string;
    location?: string;
    privacy?: string;
  }) => {
    createPost(newPost);
  }, [createPost]);

  // Memoized filtered and sorted posts using custom hook
  const filteredPosts = useMemo(() => {
    return applyFiltersToPost(posts, activeTab, sortBy);
  }, [posts, activeTab, sortBy, applyFiltersToPost]);

  // Simulate new posts notification with constant
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.7) {
        setHasNewPosts(true);
      }
    }, NEW_POSTS_CHECK_INTERVAL);

    return () => clearInterval(interval);
  }, []);

  // Check if should show load more button
  const shouldShowLoadMore = useMemo(() => {
    return filteredPosts.length >= LOAD_MORE_THRESHOLD && !isLoadingMore;
  }, [filteredPosts.length, isLoadingMore]);

  // Memoized filter props for NewsFeedFilters component
  const filterProps = useMemo(() => ({
    isOpen: showFilters,
    onClose: () => setShowFilters(false),
    onApply: handleApplyFilters,
    onClear: handleClearFilters,
    filters,
  }), [showFilters, setShowFilters, handleApplyFilters, handleClearFilters, filters]);

  // Loading skeleton count
  const skeletonCount = 5;

  return (
    <div className="w-full max-w-2xl mx-auto pt-4">
      <Stories />
      <CreatePost onCreatePost={handleCreatePost} />
      
      {/* News Feed Tabs */}
      <NewsFeedTabs 
        activeTab={activeTab} 
        onTabChange={handleTabChange} 
      />
      
      {/* Controls */}
      <div className="bg-white rounded-lg p-4 mb-4 shadow-sm border">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </Button>
          </div>
          
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Recent</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
              <SelectItem value="comments">Most Comments</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {/* Filters */}
        {showFilters && (
          <NewsFeedFilters {...filterProps} />
        )}
      </div>
      
      {/* New Posts Notification */}
      {hasNewPosts && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 flex items-center justify-between">
          <span className="text-blue-700 text-sm font-medium">
            New posts available
          </span>
          <Button
            size="sm"
            onClick={handleRefresh}
            className="flex items-center space-x-1"
          >
            <ArrowUp className="w-4 h-4" />
            <span>View</span>
          </Button>
        </div>
      )}
      
      {/* Loading State */}
      {isLoading ? (
        <div className="space-y-4">
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <NewsFeedSkeleton key={index} />
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchPosts} variant="outline">
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredPosts.map((post) => (
            <PostCard key={post.id} post={post} />
          ))}
          
          {filteredPosts.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No posts found matching your criteria.</p>
              {hasActiveFilters && (
                <Button 
                  onClick={handleClearFilters}
                  variant="outline"
                  className="mt-4"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          )}
          
          {/* Loading More Indicator */}
          {isLoadingMore && (
            <div className="flex justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
          )}
          
          {shouldShowLoadMore && (
            <div className="text-center py-4">
              <Button 
                onClick={loadMorePosts}
                variant="outline"
                disabled={isLoadingMore}
              >
                {isLoadingMore ? 'Loading...' : 'Load More'}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Home;