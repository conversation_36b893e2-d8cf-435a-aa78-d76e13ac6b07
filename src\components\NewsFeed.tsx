import React from 'react';
import PostCard from '@/components/posts/PostCard';

interface Post {
  id: string;
  user_id: string;
  content: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  reactions?: Record<string, number>;
  feeling?: string;
  location?: string;
  tagged_friends?: string[];
  privacy?: string;
  is_live?: boolean;
  isPoll?: boolean;
  pollOptions?: string[];
  pollVotes?: Record<string, number>;
}

interface NewsFeedProps {
  posts: Post[];
}

const NewsFeed: React.FC<NewsFeedProps> = ({ posts }) => {
  return (
    <div className="space-y-4">
      {posts.map((post) => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  );
};

export default NewsFeed;
