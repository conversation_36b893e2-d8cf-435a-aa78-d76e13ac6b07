import React, { useState, useEffect, useRef } from 'react';
import { Send, Smile, Paperclip, Mic, Phone, Video, MoreVertical, Search, Star, Heart, ThumbsUp, Laugh, Angry, Sad, Image, File, MapPin, Camera, MicIcon } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { storage } from '@/lib/storage';
import { getSafeImage } from '@/lib/constants';
import { EmojiPicker } from './EmojiPicker';

interface Message {
  id: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'file' | 'voice' | 'location' | 'sticker';
  timestamp: string;
  edited?: boolean;
  editedAt?: string;
  reactions: {
    [userId: string]: {
      type: 'like' | 'love' | 'laugh' | 'angry' | 'sad' | 'wow';
      timestamp: string;
    };
  };
  replyTo?: string;
  attachments?: {
    type: 'image' | 'file' | 'voice';
    url: string;
    name: string;
    size?: number;
    duration?: number;
  }[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  readBy: {
    [userId: string]: string;
  };
}

interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  participants: {
    id: string;
    name: string;
    avatar: string;
    isOnline: boolean;
    lastSeen?: string;
  }[];
  lastMessage?: Message;
  unreadCount: number;
  isPinned: boolean;
  isMuted: boolean;
  createdAt: string;
  updatedAt: string;
  groupSettings?: {
    adminIds: string[];
    canMembersAddOthers: boolean;
    canMembersEditInfo: boolean;
    description?: string;
    avatar?: string;
  };
}

interface EnhancedMessagingProps {
  className?: string;
  currentUserId?: string;
}

const EnhancedMessaging: React.FC<EnhancedMessagingProps> = ({ 
  className,
  currentUserId = 'user_1'
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [editingMessage, setEditingMessage] = useState<Message | null>(null);
  const [showAttachments, setShowAttachments] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Mock data
  const mockConversations: Conversation[] = [
    {
      id: 'conv_1',
      type: 'direct',
      participants: [
        {
          id: 'user_2',
          name: 'Sarah Johnson',
          avatar: getSafeImage('AVATARS', 1),
          isOnline: true
        }
      ],
      lastMessage: {
        id: 'msg_1',
        senderId: 'user_2',
        content: 'Hey! How are you doing?',
        type: 'text',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        reactions: {},
        readBy: {}
      },
      unreadCount: 2,
      isPinned: true,
      isMuted: false,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString()
    },
    {
      id: 'conv_2',
      type: 'group',
      name: 'Project Team',
      participants: [
        {
          id: 'user_3',
          name: 'Mike Wilson',
          avatar: getSafeImage('AVATARS', 2),
          isOnline: false,
          lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'user_4',
          name: 'Alex Chen',
          avatar: getSafeImage('AVATARS', 3),
          isOnline: true
        },
        {
          id: 'user_5',
          name: 'Emma Davis',
          avatar: getSafeImage('AVATARS', 4),
          isOnline: true
        }
      ],
      lastMessage: {
        id: 'msg_2',
        senderId: 'user_4',
        content: 'The presentation looks great!',
        type: 'text',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        reactions: {
          user_3: { type: 'like', timestamp: new Date().toISOString() }
        },
        readBy: {}
      },
      unreadCount: 0,
      isPinned: false,
      isMuted: false,
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      groupSettings: {
        adminIds: ['user_1', 'user_3'],
        canMembersAddOthers: true,
        canMembersEditInfo: false,
        description: 'Team collaboration for the Q4 project'
      }
    }
  ];

  const mockMessages: Message[] = [
    {
      id: 'msg_1',
      senderId: 'user_2',
      content: 'Hey! How are you doing?',
      type: 'text',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      reactions: {},
      readBy: { [currentUserId]: new Date().toISOString() }
    },
    {
      id: 'msg_2',
      senderId: currentUserId,
      content: "I'm doing great! Just finished a big project. How about you?",
      type: 'text',
      timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
      reactions: {
        user_2: { type: 'like', timestamp: new Date(Date.now() - 85 * 60 * 1000).toISOString() }
      },
      readBy: { user_2: new Date(Date.now() - 85 * 60 * 1000).toISOString() }
    },
    {
      id: 'msg_3',
      senderId: 'user_2',
      content: 'That\'s awesome! I\'d love to hear more about it.',
      type: 'text',
      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      reactions: {},
      readBy: { [currentUserId]: new Date().toISOString() }
    },
    {
      id: 'msg_4',
      senderId: 'user_2',
      content: 'Check out this photo from my vacation!',
      type: 'image',
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      reactions: {
        [currentUserId]: { type: 'love', timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString() }
      },
      attachments: [{
        type: 'image',
        url: getSafeImage('POSTS', 0),
        name: 'vacation_photo.jpg'
      }],
      readBy: { [currentUserId]: new Date().toISOString() }
    }
  ];

  useEffect(() => {
    setConversations(mockConversations);
    if (mockConversations.length > 0) {
      setSelectedConversation(mockConversations[0]);
      setMessages(mockMessages);
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;

    const message: Message = {
      id: `msg_${Date.now()}`,
      senderId: currentUserId,
      content: newMessage,
      type: 'text',
      timestamp: new Date().toISOString(),
      reactions: {},
      readBy: {},
      ...(replyingTo && { replyTo: replyingTo.id })
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');
    setReplyingTo(null);
    
    // Update conversation's last message
    setConversations(prev => prev.map(conv => 
      conv.id === selectedConversation.id 
        ? { ...conv, lastMessage: message, updatedAt: new Date().toISOString() }
        : conv
    ));
  };

  const handleEditMessage = (messageId: string, newContent: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { 
            ...msg, 
            content: newContent, 
            edited: true, 
            editedAt: new Date().toISOString() 
          }
        : msg
    ));
    setEditingMessage(null);
  };

  const handleReaction = (messageId: string, reactionType: 'like' | 'love' | 'laugh' | 'angry' | 'sad' | 'wow') => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const newReactions = { ...msg.reactions };
        if (newReactions[currentUserId]?.type === reactionType) {
          delete newReactions[currentUserId];
        } else {
          newReactions[currentUserId] = {
            type: reactionType,
            timestamp: new Date().toISOString()
          };
        }
        return { ...msg, reactions: newReactions };
      }
      return msg;
    }));
  };

  const handleFileUpload = (type: 'image' | 'file') => {
    if (type === 'image') {
      imageInputRef.current?.click();
    } else {
      fileInputRef.current?.click();
    }
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      toast.success('Started recording...');
      // Simulate recording
      setTimeout(() => {
        setIsRecording(false);
        toast.success('Voice message sent!');
      }, 3000);
    }
  };

  const getReactionIcon = (type: string) => {
    switch (type) {
      case 'like': return <ThumbsUp className="w-3 h-3" />;
      case 'love': return <Heart className="w-3 h-3 text-red-500" />;
      case 'laugh': return <Laugh className="w-3 h-3 text-yellow-500" />;
      case 'angry': return <Angry className="w-3 h-3 text-red-600" />;
      case 'sad': return <Sad className="w-3 h-3 text-blue-500" />;
      case 'wow': return <Star className="w-3 h-3 text-orange-500" />;
      default: return null;
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredConversations = conversations.filter(conv => {
    const searchLower = searchQuery.toLowerCase();
    if (conv.type === 'group') {
      return conv.name?.toLowerCase().includes(searchLower);
    } else {
      return conv.participants.some(p => 
        p.name.toLowerCase().includes(searchLower)
      );
    }
  });

  return (
    <div className={`flex h-[600px] bg-white dark:bg-gray-900 rounded-lg shadow-lg ${className}`}>
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => {
            const displayName = conversation.type === 'group' 
              ? conversation.name 
              : conversation.participants[0]?.name;
            const displayAvatar = conversation.type === 'group'
              ? conversation.groupSettings?.avatar || getSafeImage('AVATARS', 0)
              : conversation.participants[0]?.avatar;
            const isOnline = conversation.type === 'direct' 
              ? conversation.participants[0]?.isOnline 
              : false;

            return (
              <div
                key={conversation.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-800 ${
                  selectedConversation?.id === conversation.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                }`}
                onClick={() => {
                  setSelectedConversation(conversation);
                  setMessages(mockMessages);
                }}
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={displayAvatar} />
                      <AvatarFallback>{displayName?.[0]}</AvatarFallback>
                    </Avatar>
                    {isOnline && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-sm truncate">{displayName}</h3>
                      <div className="flex items-center gap-1">
                        {conversation.isPinned && (
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        )}
                        {conversation.lastMessage && (
                          <span className="text-xs text-gray-500">
                            {formatTime(conversation.lastMessage.timestamp)}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {conversation.lastMessage?.content || 'No messages yet'}
                      </p>
                      {conversation.unreadCount > 0 && (
                        <Badge className="bg-blue-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center">
                          {conversation.unreadCount}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10">
                  <AvatarImage src={
                    selectedConversation.type === 'group'
                      ? selectedConversation.groupSettings?.avatar || getSafeImage('AVATARS', 0)
                      : selectedConversation.participants[0]?.avatar
                  } />
                  <AvatarFallback>
                    {selectedConversation.type === 'group'
                      ? selectedConversation.name?.[0]
                      : selectedConversation.participants[0]?.name[0]
                    }
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold">
                    {selectedConversation.type === 'group'
                      ? selectedConversation.name
                      : selectedConversation.participants[0]?.name
                    }
                  </h3>
                  <p className="text-sm text-gray-500">
                    {selectedConversation.type === 'group'
                      ? `${selectedConversation.participants.length} members`
                      : selectedConversation.participants[0]?.isOnline
                        ? 'Online'
                        : 'Last seen recently'
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm">
                  <Phone className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Video className="w-4 h-4" />
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>View Profile</DropdownMenuItem>
                    <DropdownMenuItem>Mute Notifications</DropdownMenuItem>
                    <DropdownMenuItem>Search in Conversation</DropdownMenuItem>
                    <DropdownMenuItem className="text-red-600">Delete Conversation</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => {
                const isOwn = message.senderId === currentUserId;
                const sender = selectedConversation.participants.find(p => p.id === message.senderId);
                const replyToMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;
                
                return (
                  <div key={message.id} className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[70%] ${isOwn ? 'order-2' : 'order-1'}`}>
                      {!isOwn && selectedConversation.type === 'group' && (
                        <p className="text-xs text-gray-500 mb-1 ml-2">{sender?.name}</p>
                      )}
                      
                      <div className={`relative group ${
                        isOwn 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                      } rounded-2xl px-4 py-2`}>
                        {replyToMessage && (
                          <div className="border-l-2 border-gray-300 pl-2 mb-2 text-sm opacity-75">
                            <p className="font-medium">
                              {replyToMessage.senderId === currentUserId ? 'You' : sender?.name}
                            </p>
                            <p className="truncate">{replyToMessage.content}</p>
                          </div>
                        )}
                        
                        {message.type === 'image' && message.attachments?.[0] && (
                          <img
                            src={message.attachments[0].url}
                            alt="Shared image"
                            className="max-w-full h-auto rounded-lg mb-2"
                          />
                        )}
                        
                        <p className="break-words">{message.content}</p>
                        
                        {message.edited && (
                          <span className="text-xs opacity-75 ml-2">(edited)</span>
                        )}
                        
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs opacity-75">
                            {formatTime(message.timestamp)}
                          </span>
                          
                          {Object.keys(message.reactions).length > 0 && (
                            <div className="flex items-center gap-1">
                              {Object.entries(message.reactions).map(([userId, reaction]) => (
                                <div key={userId} className="flex items-center">
                                  {getReactionIcon(reaction.type)}
                                </div>
                              ))}
                              <span className="text-xs ml-1">{Object.keys(message.reactions).length}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Message Actions */}
                        <div className="absolute top-0 right-0 transform translate-x-full opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="flex items-center gap-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleReaction(message.id, 'like')}
                            >
                              <ThumbsUp className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleReaction(message.id, 'love')}
                            >
                              <Heart className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setReplyingTo(message)}
                            >
                              Reply
                            </Button>
                            {isOwn && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setEditingMessage(message)}
                              >
                                Edit
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {!isOwn && (
                      <Avatar className={`w-8 h-8 ${isOwn ? 'order-1 ml-2' : 'order-2 mr-2'}`}>
                        <AvatarImage src={sender?.avatar} />
                        <AvatarFallback>{sender?.name[0]}</AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </div>

            {/* Reply Preview */}
            {replyingTo && (
              <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-1 h-8 bg-blue-500 rounded" />
                    <div>
                      <p className="text-sm font-medium">Replying to {replyingTo.senderId === currentUserId ? 'yourself' : 'message'}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">{replyingTo.content}</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => setReplyingTo(null)}>
                    ×
                  </Button>
                </div>
              </div>
            )}

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-end gap-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAttachments(!showAttachments)}
                    >
                      <Paperclip className="w-4 h-4" />
                    </Button>
                    
                    {showAttachments && (
                      <div className="flex items-center gap-1">
                        <Button variant="ghost" size="sm" onClick={() => handleFileUpload('image')}>
                          <Image className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleFileUpload('file')}>
                          <File className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MapPin className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Camera className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-end gap-2">
                    <Textarea
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      className="min-h-[40px] max-h-[120px] resize-none"
                    />
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      >
                        <Smile className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleVoiceRecord}
                        className={isRecording ? 'text-red-500' : ''}
                      >
                        <MicIcon className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                <Button 
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  className="mb-2"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
              
              {showEmojiPicker && (
                <div className="mt-2">
                  <EmojiPicker
                    onEmojiSelect={(emoji) => {
                      setNewMessage(prev => prev + emoji);
                      setShowEmojiPicker(false);
                    }}
                  />
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Select a conversation to start messaging</p>
            </div>
          </div>
        )}
      </div>

      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            toast.success(`File "${file.name}" uploaded!`);
          }
        }}
      />
      
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            toast.success(`Image "${file.name}" uploaded!`);
          }
        }}
      />

      {/* Edit Message Dialog */}
      <Dialog open={!!editingMessage} onOpenChange={() => setEditingMessage(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Message</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              value={editingMessage?.content || ''}
              onChange={(e) => setEditingMessage(prev => 
                prev ? { ...prev, content: e.target.value } : null
              )}
              placeholder="Edit your message..."
            />
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  if (editingMessage) {
                    handleEditMessage(editingMessage.id, editingMessage.content);
                  }
                }}
              >
                Save Changes
              </Button>
              <Button variant="outline" onClick={() => setEditingMessage(null)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedMessaging;