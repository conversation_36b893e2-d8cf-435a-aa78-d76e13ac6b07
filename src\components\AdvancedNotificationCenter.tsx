import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  Heart, 
  MessageCircle, 
  UserPlus, 
  Calendar, 
  Tag, 
  Share2, 
  Gift,
  Briefcase,
  Users,
  Video,
  X,
  Check,
  Settings,
  Filter,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';

type NotificationType = 
  | 'like' 
  | 'comment' 
  | 'share' 
  | 'friend_request' 
  | 'friend_accepted'
  | 'tag' 
  | 'event' 
  | 'birthday' 
  | 'job' 
  | 'group' 
  | 'video_call'
  | 'live_stream'
  | 'memory';

interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  isImportant: boolean;
  actionUrl?: string;
  actor: {
    id: string;
    name: string;
    avatar: string;
  };
  metadata?: {
    postId?: string;
    eventId?: string;
    groupId?: string;
    callId?: string;
  };
}

interface AdvancedNotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  onMarkAsRead: (notificationId: string) => void;
  onMarkAllAsRead: () => void;
  onDeleteNotification: (notificationId: string) => void;
  onNotificationClick: (notification: Notification) => void;
}

const AdvancedNotificationCenter: React.FC<AdvancedNotificationCenterProps> = ({
  isOpen,
  onClose,
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onDeleteNotification,
  onNotificationClick
}) => {
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'important'>('all');
  const [filter, setFilter] = useState<NotificationType | 'all'>('all');

  const getNotificationIcon = (type: NotificationType) => {
    const iconMap = {
      like: Heart,
      comment: MessageCircle,
      share: Share2,
      friend_request: UserPlus,
      friend_accepted: UserPlus,
      tag: Tag,
      event: Calendar,
      birthday: Gift,
      job: Briefcase,
      group: Users,
      video_call: Video,
      live_stream: Video,
      memory: Heart
    };
    return iconMap[type] || Bell;
  };

  const getNotificationColor = (type: NotificationType) => {
    const colorMap = {
      like: 'text-red-500',
      comment: 'text-blue-500',
      share: 'text-green-500',
      friend_request: 'text-purple-500',
      friend_accepted: 'text-green-500',
      tag: 'text-yellow-500',
      event: 'text-indigo-500',
      birthday: 'text-pink-500',
      job: 'text-orange-500',
      group: 'text-teal-500',
      video_call: 'text-blue-600',
      live_stream: 'text-red-600',
      memory: 'text-purple-600'
    };
    return colorMap[type] || 'text-gray-500';
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'unread' && notification.isRead) return false;
    if (activeTab === 'important' && !notification.isImportant) return false;
    if (filter !== 'all' && notification.type !== filter) return false;
    return true;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const importantCount = notifications.filter(n => n.isImportant && !n.isRead).length;

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      onMarkAsRead(notification.id);
    }
    onNotificationClick(notification);
  };

  const handleQuickAction = (notification: Notification, action: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    switch (action) {
      case 'accept':
        toast.success('Friend request accepted');
        break;
      case 'decline':
        toast.info('Friend request declined');
        break;
      case 'join':
        toast.success('Joined the event');
        break;
      case 'like':
        toast.success('Liked the post');
        break;
      default:
        break;
    }
    
    onMarkAsRead(notification.id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-16">
      <Card className="w-full max-w-md mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Notifications</h2>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">
                All
                {notifications.length > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {notifications.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="unread">
                Unread
                {unreadCount > 0 && (
                  <Badge variant="destructive" className="ml-2 text-xs">
                    {unreadCount}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="important">
                Important
                {importantCount > 0 && (
                  <Badge variant="default" className="ml-2 text-xs">
                    {importantCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Actions */}
          <div className="flex items-center justify-between mt-4">
            <Button 
              variant="outline" 
              size="sm"
              onClick={onMarkAllAsRead}
              disabled={unreadCount === 0}
            >
              <Check className="w-4 h-4 mr-2" />
              Mark all read
            </Button>
            <Button variant="ghost" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Notifications List */}
        <div className="overflow-y-auto max-h-96">
          {filteredNotifications.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Bell className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No notifications</p>
              <p className="text-sm mt-1">You're all caught up!</p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredNotifications.map((notification) => {
                const IconComponent = getNotificationIcon(notification.type);
                const iconColor = getNotificationColor(notification.type);
                
                return (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer border-l-4 transition-colors ${
                      notification.isRead 
                        ? 'border-transparent' 
                        : notification.isImportant 
                          ? 'border-red-500 bg-red-50/50 dark:bg-red-900/10' 
                          : 'border-blue-500 bg-blue-50/50 dark:bg-blue-900/10'
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="relative">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={notification.actor.avatar} />
                          <AvatarFallback>
                            {notification.actor.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center`}>
                          <IconComponent className={`w-3 h-3 ${iconColor}`} />
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={`text-sm ${notification.isRead ? 'text-gray-600 dark:text-gray-400' : 'text-gray-900 dark:text-white font-medium'}`}>
                              <span className="font-semibold">{notification.actor.name}</span>{' '}
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                            </p>
                          </div>
                          
                          <div className="flex items-center space-x-1 ml-2">
                            {notification.isImportant && (
                              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onDeleteNotification(notification.id);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                        
                        {/* Quick Actions */}
                        {notification.type === 'friend_request' && (
                          <div className="flex items-center space-x-2 mt-2">
                            <Button 
                              size="sm" 
                              onClick={(e) => handleQuickAction(notification, 'accept', e)}
                              className="h-7 text-xs"
                            >
                              Accept
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={(e) => handleQuickAction(notification, 'decline', e)}
                              className="h-7 text-xs"
                            >
                              Decline
                            </Button>
                          </div>
                        )}
                        
                        {notification.type === 'event' && (
                          <div className="flex items-center space-x-2 mt-2">
                            <Button 
                              size="sm"
                              onClick={(e) => handleQuickAction(notification, 'join', e)}
                              className="h-7 text-xs"
                            >
                              Join Event
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AdvancedNotificationCenter;
