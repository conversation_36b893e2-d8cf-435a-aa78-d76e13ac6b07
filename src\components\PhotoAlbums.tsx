import React, { useState, useEffect } from 'react';
import { Plus, Camera, Image, MoreHorizontal, Heart, MessageCircle, Share, Eye, Lock, Users, Globe, Check } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { MOCK_IMAGES, getSafeImage } from '@/lib/constants';
import { toast } from 'sonner';
import { storage } from '@/lib/storage';

interface Photo {
  id: string;
  url: string;
  caption?: string;
  timestamp: string;
  likes: number;
  comments: number;
  isLiked: boolean;
}

interface Album {
  id: string;
  title: string;
  description?: string;
  coverPhoto: string;
  photos: Photo[];
  privacy: 'public' | 'friends' | 'only_me';
  createdAt: string;
  updatedAt: string;
  author: {
    name: string;
    avatar: string;
  };
}

interface PhotoAlbumsProps {
  userId?: string;
  isOwnProfile?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
  onSelectPhotos?: (photos: string[]) => void;
}

const PhotoAlbums: React.FC<PhotoAlbumsProps> = ({ userId, isOwnProfile = false, isOpen, onClose, onSelectPhotos }) => {
  const [albums, setAlbums] = useState<Album[]>([]);
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isAlbumViewOpen, setIsAlbumViewOpen] = useState(false);
  const [newAlbum, setNewAlbum] = useState({
    title: '',
    description: '',
    privacy: 'friends' as const
  });

  // Load albums from storage
  useEffect(() => {
    const savedAlbums = storage.get<Album[]>('photo_albums', []);
    if (savedAlbums.length === 0) {
      // Initialize with mock data
      const mockAlbums: Album[] = [
        {
          id: 'album_1',
          title: 'Vacation 2024',
          description: 'Amazing trip to the mountains',
          coverPhoto: getSafeImage('POSTS', 0),
          privacy: 'friends',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          author: {
            name: 'John Doe',
            avatar: getSafeImage('AVATARS', 0)
          },
          photos: [
            {
              id: 'photo_1',
              url: getSafeImage('POSTS', 0),
              caption: 'Beautiful mountain view',
              timestamp: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
              likes: 24,
              comments: 5,
              isLiked: false
            },
            {
              id: 'photo_2',
              url: getSafeImage('POSTS', 1),
              caption: 'Sunset at the peak',
              timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
              likes: 31,
              comments: 8,
              isLiked: true
            },
            {
              id: 'photo_3',
              url: getSafeImage('POSTS', 2),
              caption: 'Group photo with friends',
              timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
              likes: 45,
              comments: 12,
              isLiked: false
            }
          ]
        },
        {
          id: 'album_2',
          title: 'Family Moments',
          description: 'Precious memories with family',
          coverPhoto: getSafeImage('POSTS', 3),
          privacy: 'only_me',
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          author: {
            name: 'John Doe',
            avatar: getSafeImage('AVATARS', 0)
          },
          photos: [
            {
              id: 'photo_4',
              url: getSafeImage('POSTS', 3),
              caption: 'Family dinner',
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
              likes: 18,
              comments: 3,
              isLiked: true
            },
            {
              id: 'photo_5',
              url: getSafeImage('POSTS', 4),
              caption: 'Birthday celebration',
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              likes: 52,
              comments: 15,
              isLiked: false
            }
          ]
        }
      ];
      setAlbums(mockAlbums);
      storage.set('photo_albums', mockAlbums);
    } else {
      setAlbums(savedAlbums);
    }
  }, []);

  const handleCreateAlbum = () => {
    if (!newAlbum.title.trim()) {
      toast.error('Album title is required');
      return;
    }

    const album: Album = {
      id: `album_${Date.now()}`,
      title: newAlbum.title,
      description: newAlbum.description,
      coverPhoto: getSafeImage('POSTS', Math.floor(Math.random() * 6)),
      privacy: newAlbum.privacy,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: {
        name: 'You',
        avatar: getSafeImage('AVATARS', 0)
      },
      photos: []
    };

    const updatedAlbums = [...albums, album];
    setAlbums(updatedAlbums);
    storage.set('photo_albums', updatedAlbums);
    
    setNewAlbum({ title: '', description: '', privacy: 'friends' });
    setIsCreateModalOpen(false);
    toast.success('Album created successfully!');
  };

  const handleLikePhoto = (albumId: string, photoId: string) => {
    const updatedAlbums = albums.map(album => {
      if (album.id === albumId) {
        return {
          ...album,
          photos: album.photos.map(photo => {
            if (photo.id === photoId) {
              return {
                ...photo,
                isLiked: !photo.isLiked,
                likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1
              };
            }
            return photo;
          })
        };
      }
      return album;
    });
    
    setAlbums(updatedAlbums);
    storage.set('photo_albums', updatedAlbums);
    
    if (selectedAlbum && selectedAlbum.id === albumId) {
      setSelectedAlbum(updatedAlbums.find(a => a.id === albumId) || null);
    }
  };

  const getPrivacyIcon = (privacy: string) => {
    switch (privacy) {
      case 'public': return <Globe className="w-4 h-4" />;
      case 'friends': return <Users className="w-4 h-4" />;
      case 'only_me': return <Lock className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const getPrivacyText = (privacy: string) => {
    switch (privacy) {
      case 'public': return 'Public';
      case 'friends': return 'Friends';
      case 'only_me': return 'Only Me';
      default: return 'Friends';
    }
  };

  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(!!onSelectPhotos);

  const handlePhotoSelect = (photoUrl: string) => {
    if (!isSelectionMode) return;
    
    setSelectedPhotos(prev => {
      if (prev.includes(photoUrl)) {
        return prev.filter(url => url !== photoUrl);
      } else {
        return [...prev, photoUrl];
      }
    });
  };

  const handleConfirmSelection = () => {
    if (onSelectPhotos && selectedPhotos.length > 0) {
      onSelectPhotos(selectedPhotos);
      onClose?.();
    }
  };

  const content = (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Photo Albums</h2>
          <p className="text-gray-600 dark:text-gray-400">{albums.length} albums</p>
        </div>
        {isOwnProfile && (
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Create Album
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Album</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Album Title</label>
                  <Input
                    placeholder="Enter album title"
                    value={newAlbum.title}
                    onChange={(e) => setNewAlbum(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Description (Optional)</label>
                  <Textarea
                    placeholder="Describe your album"
                    value={newAlbum.description}
                    onChange={(e) => setNewAlbum(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Privacy</label>
                  <Select value={newAlbum.privacy} onValueChange={(value: any) => setNewAlbum(prev => ({ ...prev, privacy: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">🌍 Public</SelectItem>
                      <SelectItem value="friends">👥 Friends</SelectItem>
                      <SelectItem value="only_me">🔒 Only Me</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateAlbum} className="flex-1">
                    Create Album
                  </Button>
                  <Button variant="outline" onClick={() => setIsCreateModalOpen(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Albums Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {albums.map((album) => (
          <Card key={album.id} className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => {
            setSelectedAlbum(album);
            setIsAlbumViewOpen(true);
          }}>
            <div className="relative">
              <img
                src={album.coverPhoto}
                alt={album.title}
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  {getPrivacyIcon(album.privacy)}
                  {getPrivacyText(album.privacy)}
                </Badge>
              </div>
              <div className="absolute bottom-2 right-2">
                <Badge variant="secondary">
                  {album.photos.length} photos
                </Badge>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="font-semibold text-lg mb-1">{album.title}</h3>
              {album.description && (
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
                  {album.description}
                </p>
              )}
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Avatar className="w-6 h-6">
                  <AvatarImage src={album.author.avatar} />
                  <AvatarFallback>{album.author.name[0]}</AvatarFallback>
                </Avatar>
                <span>{album.author.name}</span>
                <span>•</span>
                <span>{new Date(album.updatedAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Album Viewer Dialog */}
      <Dialog open={isAlbumViewOpen} onOpenChange={setIsAlbumViewOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedAlbum && (
            <>
              <DialogHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <DialogTitle className="text-xl">{selectedAlbum.title}</DialogTitle>
                    {selectedAlbum.description && (
                      <p className="text-gray-600 dark:text-gray-400 mt-1">
                        {selectedAlbum.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="flex items-center gap-1">
                      {getPrivacyIcon(selectedAlbum.privacy)}
                      {getPrivacyText(selectedAlbum.privacy)}
                    </Badge>
                  </div>
                </div>
              </DialogHeader>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                {selectedAlbum.photos.map((photo) => (
                  <div key={photo.id} className="space-y-2">
                    <div 
                      className={`relative group cursor-pointer ${
                        isSelectionMode ? 'hover:ring-2 hover:ring-blue-500' : ''
                      } ${
                        selectedPhotos.includes(photo.url) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handlePhotoSelect(photo.url)}
                    >
                      <img
                        src={photo.url}
                        alt={photo.caption || 'Photo'}
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg" />
                      {isSelectionMode && selectedPhotos.includes(photo.url) && (
                        <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                          <Check className="w-4 h-4" />
                        </div>
                      )}
                    </div>
                    
                    {photo.caption && (
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {photo.caption}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`flex items-center gap-1 ${photo.isLiked ? 'text-red-500' : ''}`}
                          onClick={() => handleLikePhoto(selectedAlbum.id, photo.id)}
                        >
                          <Heart className={`w-4 h-4 ${photo.isLiked ? 'fill-current' : ''}`} />
                          {photo.likes}
                        </Button>
                        <Button variant="ghost" size="sm" className="flex items-center gap-1">
                          <MessageCircle className="w-4 h-4" />
                          {photo.comments}
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Share className="w-4 h-4" />
                        </Button>
                      </div>
                      <span className="text-xs text-gray-500">
                        {new Date(photo.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedAlbum.photos.length === 0 && (
                <div className="text-center py-12">
                  <Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No photos in this album yet</p>
                  {isOwnProfile && (
                    <Button className="mt-4" variant="outline">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Photos
                    </Button>
                  )}
                </div>
              )}
              {isSelectionMode && selectedPhotos.length > 0 && (
                <div className="flex gap-2 pt-4 border-t">
                  <Button onClick={handleConfirmSelection} className="flex-1">
                    Select {selectedPhotos.length} Photo{selectedPhotos.length > 1 ? 's' : ''}
                  </Button>
                  <Button variant="outline" onClick={() => setSelectedPhotos([])}>
                    Clear
                  </Button>
                </div>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );

  // If used as a dialog component
  if (isOpen !== undefined && onClose) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Image className="w-5 h-5" />
              {isSelectionMode ? 'Select Photos from Albums' : 'Photo Albums'}
            </DialogTitle>
          </DialogHeader>
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  // Default standalone component
  return content;
};

export default PhotoAlbums;