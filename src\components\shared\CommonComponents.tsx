import React from 'react';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  Bookmark, 
  MoreHorizontal,
  Users,
  Calendar,
  MapPin,
  Clock,
  Eye,
  ThumbsUp,
  Smile,
  Camera,
  Video,
  Mic,
  Send
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

// Common User Avatar Component
interface UserAvatarProps {
  user: {
    id: string;
    name?: string;
    avatar?: string;
  };
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showOnlineStatus?: boolean;
  isOnline?: boolean;
  className?: string;
  onClick?: () => void;
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  size = 'md',
  showOnlineStatus = false,
  isOnline = false,
  className,
  onClick
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  };

  return (
    <div className="relative">
      <Avatar 
        className={cn(sizeClasses[size], className, onClick && 'cursor-pointer')}
        onClick={onClick}
      >
        <AvatarImage src={user.avatar} />
        <AvatarFallback>
          {user.name?.charAt(0)?.toUpperCase() || 'U'}
        </AvatarFallback>
      </Avatar>
      {showOnlineStatus && (
        <div className={cn(
          "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white",
          isOnline ? 'bg-green-500' : 'bg-gray-400'
        )} />
      )}
    </div>
  );
};

// Common Interaction Bar Component
interface InteractionBarProps {
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  isSaved: boolean;
  onLike: () => void;
  onComment: () => void;
  onShare: () => void;
  onSave: () => void;
  showCounts?: boolean;
  variant?: 'default' | 'compact';
}

export const InteractionBar: React.FC<InteractionBarProps> = ({
  likes,
  comments,
  shares,
  isLiked,
  isSaved,
  onLike,
  onComment,
  onShare,
  onSave,
  showCounts = true,
  variant = 'default'
}) => {
  const buttonSize = variant === 'compact' ? 'sm' : 'default';
  const iconSize = variant === 'compact' ? 'w-4 h-4' : 'w-5 h-5';

  return (
    <div className="flex items-center justify-between pt-3 border-t">
      <div className="flex items-center space-x-1">
        <Button
          variant="ghost"
          size={buttonSize}
          onClick={onLike}
          className={cn(
            "flex items-center space-x-2",
            isLiked && "text-red-500 hover:text-red-600"
          )}
        >
          <Heart className={cn(iconSize, isLiked && "fill-current")} />
          {showCounts && <span>{likes}</span>}
        </Button>
        
        <Button
          variant="ghost"
          size={buttonSize}
          onClick={onComment}
          className="flex items-center space-x-2"
        >
          <MessageCircle className={iconSize} />
          {showCounts && <span>{comments}</span>}
        </Button>
        
        <Button
          variant="ghost"
          size={buttonSize}
          onClick={onShare}
          className="flex items-center space-x-2"
        >
          <Share2 className={iconSize} />
          {showCounts && <span>{shares}</span>}
        </Button>
      </div>
      
      <Button
        variant="ghost"
        size={buttonSize}
        onClick={onSave}
        className={cn(
          "flex items-center space-x-2",
          isSaved && "text-blue-500 hover:text-blue-600"
        )}
      >
        <Bookmark className={cn(iconSize, isSaved && "fill-current")} />
      </Button>
    </div>
  );
};

// Common Post Header Component
interface PostHeaderProps {
  user: {
    id: string;
    name?: string;
    avatar?: string;
  };
  timestamp: string;
  location?: string;
  feeling?: string;
  privacy?: string;
  isLive?: boolean;
  onUserClick?: () => void;
  onMenuClick?: () => void;
}

export const PostHeader: React.FC<PostHeaderProps> = ({
  user,
  timestamp,
  location,
  feeling,
  privacy,
  isLive,
  onUserClick,
  onMenuClick
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <UserAvatar user={user} onClick={onUserClick} />
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h3 
              className="font-semibold text-sm cursor-pointer hover:underline"
              onClick={onUserClick}
            >
              {user.name || 'Unknown User'}
            </h3>
            {isLive && (
              <Badge variant="destructive" className="text-xs">
                LIVE
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>{formatDistanceToNow(new Date(timestamp), { addSuffix: true })}</span>
            {feeling && (
              <>
                <span>•</span>
                <span className="flex items-center space-x-1">
                  <Smile className="w-3 h-3" />
                  <span>{feeling}</span>
                </span>
              </>
            )}
            {location && (
              <>
                <span>•</span>
                <span className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span>{location}</span>
                </span>
              </>
            )}
          </div>
        </div>
      </div>
      
      {onMenuClick && (
        <Button variant="ghost" size="sm" onClick={onMenuClick}>
          <MoreHorizontal className="w-4 h-4" />
        </Button>
      )}
    </div>
  );
};

// Common Empty State Component
interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action
}) => {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 dark:bg-gray-800">
        {icon}
      </div>
      <h3 className="text-lg font-medium mb-2 dark:text-white">{title}</h3>
      <p className="text-gray-500 mb-4 dark:text-gray-400">{description}</p>
      {action && (
        <Button onClick={action.onClick}>
          {action.label}
        </Button>
      )}
    </div>
  );
};

// Common Loading Skeleton Component
interface LoadingSkeletonProps {
  type: 'post' | 'user' | 'comment' | 'story';
  count?: number;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  type,
  count = 1
}) => {
  const renderSkeleton = () => {
    switch (type) {
      case 'post':
        return (
          <Card className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 w-32 bg-gray-200 rounded dark:bg-gray-700"></div>
                  <div className="h-3 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
                </div>
              </div>
              <div className="space-y-2 mb-4">
                <div className="h-4 w-full bg-gray-200 rounded dark:bg-gray-700"></div>
                <div className="h-4 w-3/4 bg-gray-200 rounded dark:bg-gray-700"></div>
              </div>
              <div className="h-48 w-full bg-gray-200 rounded dark:bg-gray-700 mb-4"></div>
              <div className="flex space-x-4">
                <div className="h-8 w-16 bg-gray-200 rounded dark:bg-gray-700"></div>
                <div className="h-8 w-16 bg-gray-200 rounded dark:bg-gray-700"></div>
                <div className="h-8 w-16 bg-gray-200 rounded dark:bg-gray-700"></div>
              </div>
            </CardContent>
          </Card>
        );
      
      case 'user':
        return (
          <div className="flex items-center space-x-3 animate-pulse">
            <div className="w-10 h-10 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="space-y-2 flex-1">
              <div className="h-4 w-32 bg-gray-200 rounded dark:bg-gray-700"></div>
              <div className="h-3 w-24 bg-gray-200 rounded dark:bg-gray-700"></div>
            </div>
          </div>
        );
      
      case 'comment':
        return (
          <div className="flex items-start space-x-3 animate-pulse">
            <div className="w-8 h-8 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="flex-1 space-y-2">
              <div className="h-3 w-20 bg-gray-200 rounded dark:bg-gray-700"></div>
              <div className="h-4 w-full bg-gray-200 rounded dark:bg-gray-700"></div>
              <div className="h-4 w-2/3 bg-gray-200 rounded dark:bg-gray-700"></div>
            </div>
          </div>
        );
      
      case 'story':
        return (
          <div className="flex flex-col items-center space-y-2 animate-pulse">
            <div className="w-16 h-16 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            <div className="h-3 w-12 bg-gray-200 rounded dark:bg-gray-700"></div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>
          {renderSkeleton()}
        </div>
      ))}
    </div>
  );
};

// Common Stats Display Component
interface StatsDisplayProps {
  stats: Array<{
    label: string;
    value: number | string;
    icon?: React.ReactNode;
    color?: string;
  }>;
  layout?: 'horizontal' | 'vertical';
}

export const StatsDisplay: React.FC<StatsDisplayProps> = ({
  stats,
  layout = 'horizontal'
}) => {
  const containerClass = layout === 'horizontal' 
    ? 'flex items-center space-x-6' 
    : 'space-y-4';

  return (
    <div className={containerClass}>
      {stats.map((stat, index) => (
        <div key={index} className="flex items-center space-x-2">
          {stat.icon && (
            <div className={cn("w-4 h-4", stat.color)}>
              {stat.icon}
            </div>
          )}
          <div className="text-center">
            <div className="font-semibold text-lg">{stat.value}</div>
            <div className="text-sm text-gray-500">{stat.label}</div>
          </div>
        </div>
      ))}
    </div>
  );
};
