import React, { Suspense, lazy } from "react";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryProvider } from "@/providers/QueryProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import AppLayout from "@/components/layout/AppLayout";
import ErrorBoundary from "@/components/ui/ErrorBoundary";
import { ROUTES } from "@/lib/constants";
import { Toaster } from "@/components/ui/sonner";
import ThemeProvider from "@/components/ThemeProvider";
import {
  HomeSuspenseFallback,
  MessagesSuspenseFallback,
  WatchSuspenseFallback,
  ProfileSuspenseFallback,
  PageSuspenseFallback
} from "@/components/ui/RouteSuspenseFallbacks";

// Custom suspense fallback with enhanced UI
const SuspenseFallback = () => (
  <div className="min-h-[80vh] flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center space-y-4">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 dark:border-blue-800 rounded-full animate-spin mx-auto"></div>
        <div className="absolute inset-0 w-8 h-8 border-2 border-t-blue-300 border-blue-100 dark:border-blue-600 rounded-full animate-spin-reverse mx-auto mt-2 ml-2"></div>
      </div>
      <div className="space-y-2">
        <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
          Loading page...
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Please wait while we prepare your content
        </div>
      </div>
      {/* Skeleton placeholder to maintain layout stability */}
      <div className="max-w-md mx-auto space-y-3 pt-6">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
      </div>
    </div>
  </div>
);

// Lazy-loaded pages for better performance
const Auth = lazy(() => import("./pages/Auth"));
const Home = lazy(() => import("./pages/Home"));
const Profile = lazy(() => import("./pages/Profile"));
const Friends = lazy(() => import("./pages/Friends"));
const Messages = lazy(() => import("./pages/Messages"));
const Notifications = lazy(() => import("./pages/Notifications"));
const Watch = lazy(() => import("./pages/Watch"));
const Marketplace = lazy(() => import("./pages/Marketplace"));
const Groups = lazy(() => import("./pages/Groups"));
const Events = lazy(() => import("./pages/Events"));
const Saved = lazy(() => import("./pages/Saved"));
const Memories = lazy(() => import("./pages/Memories"));
const Recent = lazy(() => import("./pages/Recent"));
const Pages = lazy(() => import("./pages/Pages"));
const Settings = lazy(() => import("./pages/Settings"));
const Search = lazy(() => import("./pages/Search"));
const Gaming = lazy(() => import("./pages/Gaming"));
import ReelsPage from "./pages/Reels";
const Weather = lazy(() => import("./pages/Weather"));
const Dating = lazy(() => import("./pages/Dating"));
const Jobs = lazy(() => import("./pages/Jobs"));
const BusinessManager = lazy(() => import("./pages/BusinessManager"));
const VideoWatch = lazy(() => import("./pages/VideoWatch"));
const ReelsWatch = lazy(() => import("./pages/ReelsWatch"));
const PageDetail = lazy(() => import("./pages/PageDetail"));
const GroupDetail = lazy(() => import("./pages/GroupDetail"));
const LiveStream = lazy(() => import("./pages/LiveStream"));
const NotFound = lazy(() => import("./pages/NotFound"));

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <TooltipProvider>
          <AuthProvider>
            <QueryProvider>
              <ThemeProvider>
                <Suspense fallback={<SuspenseFallback />}>
                  <Routes>
                    {/* Auth route - no layout */}
                    <Route path={ROUTES.AUTH} element={<Auth />} />
                    
                    {/* Main routes with layout and specific suspense fallbacks */}
                    <Route path={ROUTES.HOME} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<HomeSuspenseFallback />}>
                            <Home />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.PROFILE} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<ProfileSuspenseFallback />}>
                            <Profile />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.GROUPS} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Groups" />}>
                            <Groups />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.FRIENDS} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Friends" />}>
                            <Friends />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.MESSAGES} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<MessagesSuspenseFallback />}>
                            <Messages />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.NOTIFICATIONS} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Notifications" />}>
                            <Notifications />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.WATCH} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<WatchSuspenseFallback />}>
                            <Watch />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    
                    {/* Video watch page */}
                    <Route path="/watch/:videoId" element={
                      <AppLayout>
                        <VideoWatch />
                      </AppLayout>
                    } />
                    
                    <Route path={ROUTES.MARKETPLACE} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Marketplace" />}>
                            <Marketplace />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.EVENTS} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Events" />}>
                            <Events />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.SAVED} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Saved" />}>
                            <Saved />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.MEMORIES} element={
                      <AppLayout>
                        <ErrorBoundary>
                          <Suspense fallback={<PageSuspenseFallback title="Memories" />}>
                            <Memories />
                          </Suspense>
                        </ErrorBoundary>
                      </AppLayout>
                    } />
                    <Route path={ROUTES.SETTINGS} element={
                      <AppLayout>
                        <Settings />
                      </AppLayout>
                    } />
                    
                    {/* Pages routes */}
                    <Route path="/pages" element={
                      <AppLayout>
                        <Pages />
                      </AppLayout>
                    } />
                    <Route path="/pages/:pageId" element={
                      <AppLayout>
                        <PageDetail />
                      </AppLayout>
                    } />
                    
                    {/* Groups routes */}
                    <Route path="/groups/:groupId" element={
                      <AppLayout>
                        <GroupDetail />
                      </AppLayout>
                    } />
                    
                    {/* Other routes */}
                    <Route path="/search" element={
                      <AppLayout>
                        <Search />
                      </AppLayout>
                    } />
                    <Route path="/gaming" element={
                      <AppLayout>
                        <Gaming />
                      </AppLayout>
                    } />
                    <Route path="/reels" element={
                      <AppLayout>
                        <ReelsPage />
                      </AppLayout>
                    } />
                    <Route path="/reels/:reelId" element={<ReelsWatch />} />
                    <Route path="/recent" element={
                      <AppLayout>
                        <Recent />
                      </AppLayout>
                    } />
                    <Route path="/weather" element={
                      <AppLayout>
                        <Weather />
                      </AppLayout>
                    } />
                    <Route path={ROUTES.DATING} element={
                      <AppLayout>
                        <Dating />
                      </AppLayout>
                    } />
                    <Route path={ROUTES.JOBS} element={
                      <AppLayout>
                        <Jobs />
                      </AppLayout>
                    } />
                    <Route path={ROUTES.BUSINESS} element={
                      <AppLayout>
                        <BusinessManager />
                      </AppLayout>
                    } />
                    <Route path="/live" element={
                      <AppLayout>
                        <LiveStream />
                      </AppLayout>
                    } />
                    
                    {/* 404 route */}
                    <Route path="/not-found" element={<NotFound />} />
                    <Route path="*" element={<Navigate to="/not-found" replace />} />
                  </Routes>
                </Suspense>
                <Toaster 
                  position="top-right" 
                  closeButton
                  toastOptions={{
                    duration: 3000,
                    className: 'group toast overflow-hidden',
                  }}
                  richColors 
                  expand={false}
                  visibleToasts={3}
                />
              </ThemeProvider>
            </QueryProvider>
          </AuthProvider>
        </TooltipProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;