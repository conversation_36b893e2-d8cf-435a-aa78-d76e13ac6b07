import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  Clock, 
  Users, 
  MapPin, 
  Calendar, 
  Hash, 
  Image, 
  Video, 
  FileText,
  TrendingUp,
  History
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
// Simple checkbox component replacement
const Checkbox: React.FC<{
  id?: string;
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
}> = ({ id, checked, onCheckedChange }) => (
  <input
    id={id}
    type="checkbox"
    checked={checked}
    onChange={(e) => onCheckedChange(e.target.checked)}
    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
  />
);
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UserAvatar, LoadingSkeleton, EmptyState } from '@/components/shared';
import { debounce } from '@/utils/performanceOptimizer';
import { toast } from 'sonner';

interface SearchResult {
  id: string;
  type: 'user' | 'post' | 'page' | 'group' | 'event' | 'hashtag';
  title: string;
  description?: string;
  avatar?: string;
  thumbnail?: string;
  timestamp?: string;
  location?: string;
  relevanceScore: number;
  metadata?: Record<string, any>;
}

interface SearchFilters {
  type: string[];
  dateRange: string;
  location: string;
  sortBy: string;
  hasMedia: boolean;
  verified: boolean;
}

interface EnhancedSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onResultSelect: (result: SearchResult) => void;
  initialQuery?: string;
}

const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  isOpen,
  onClose,
  onResultSelect,
  initialQuery = ''
}) => {
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'people' | 'posts' | 'pages' | 'groups'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [trendingSearches, setTrendingSearches] = useState<string[]>([]);
  
  const [filters, setFilters] = useState<SearchFilters>({
    type: [],
    dateRange: 'all',
    location: '',
    sortBy: 'relevance',
    hasMedia: false,
    verified: false
  });

  // Mock data for demonstration
  const mockResults: SearchResult[] = [
    {
      id: '1',
      type: 'user',
      title: 'John Doe',
      description: 'Software Engineer at Tech Corp',
      avatar: '/api/placeholder/40/40',
      relevanceScore: 0.95,
      metadata: { verified: true, mutualFriends: 5 }
    },
    {
      id: '2',
      type: 'post',
      title: 'Amazing sunset at the beach',
      description: 'Just captured this beautiful moment...',
      thumbnail: '/api/placeholder/200/150',
      timestamp: '2 hours ago',
      location: 'Malibu Beach',
      relevanceScore: 0.88,
      metadata: { likes: 124, comments: 23, hasMedia: true }
    },
    {
      id: '3',
      type: 'group',
      title: 'Photography Enthusiasts',
      description: 'A community for photography lovers',
      avatar: '/api/placeholder/40/40',
      relevanceScore: 0.82,
      metadata: { members: 15420, posts: 1250 }
    },
    {
      id: '4',
      type: 'hashtag',
      title: '#photography',
      description: '2.5M posts',
      relevanceScore: 0.75,
      metadata: { postCount: 2500000, trending: true }
    }
  ];

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        return;
      }

      setIsLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Filter mock results based on query and filters
        const filteredResults = mockResults.filter(result => {
          const matchesQuery = result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                              result.description?.toLowerCase().includes(searchQuery.toLowerCase());
          
          const matchesType = filters.type.length === 0 || filters.type.includes(result.type);
          const matchesTab = activeTab === 'all' || 
                           (activeTab === 'people' && result.type === 'user') ||
                           (activeTab === 'posts' && result.type === 'post') ||
                           (activeTab === 'pages' && result.type === 'page') ||
                           (activeTab === 'groups' && result.type === 'group');
          
          const matchesMedia = !filters.hasMedia || result.metadata?.hasMedia;
          const matchesVerified = !filters.verified || result.metadata?.verified;
          
          return matchesQuery && matchesType && matchesTab && matchesMedia && matchesVerified;
        });

        // Sort results
        const sortedResults = filteredResults.sort((a, b) => {
          switch (filters.sortBy) {
            case 'recent':
              return new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime();
            case 'popular':
              return (b.metadata?.likes || 0) - (a.metadata?.likes || 0);
            default:
              return b.relevanceScore - a.relevanceScore;
          }
        });

        setResults(sortedResults);
        
        // Add to search history
        if (searchQuery.trim() && !searchHistory.includes(searchQuery)) {
          setSearchHistory(prev => [searchQuery, ...prev.slice(0, 9)]);
        }
      } catch (error) {
        console.error('Search error:', error);
        toast.error('Search failed. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [filters, activeTab, searchHistory]
  );

  // Effect to trigger search when query or filters change
  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  // Load trending searches on mount
  useEffect(() => {
    setTrendingSearches([
      'React development',
      'Photography tips',
      'Travel destinations',
      'Cooking recipes',
      'Fitness motivation'
    ]);
  }, []);

  const handleResultClick = (result: SearchResult) => {
    onResultSelect(result);
    onClose();
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      type: [],
      dateRange: 'all',
      location: '',
      sortBy: 'relevance',
      hasMedia: false,
      verified: false
    });
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'user': return Users;
      case 'post': return FileText;
      case 'page': return FileText;
      case 'group': return Users;
      case 'event': return Calendar;
      case 'hashtag': return Hash;
      default: return Search;
    }
  };

  const getResultTypeLabel = (type: string) => {
    switch (type) {
      case 'user': return 'Person';
      case 'post': return 'Post';
      case 'page': return 'Page';
      case 'group': return 'Group';
      case 'event': return 'Event';
      case 'hashtag': return 'Hashtag';
      default: return type;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-start justify-center pt-16">
      <Card className="w-full max-w-4xl mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle>Enhanced Search</CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder="Search for people, posts, pages, groups..."
              className="pl-10 pr-12"
              autoFocus
            />
            {query && (
              <button
                onClick={() => setQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Tabs and Filters */}
          <div className="flex items-center justify-between">
            <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="people">People</TabsTrigger>
                <TabsTrigger value="posts">Posts</TabsTrigger>
                <TabsTrigger value="pages">Pages</TabsTrigger>
                <TabsTrigger value="groups">Groups</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
              {(filters.type.length > 0 || filters.hasMedia || filters.verified) && (
                <Badge variant="secondary" className="ml-2">
                  {filters.type.length + (filters.hasMedia ? 1 : 0) + (filters.verified ? 1 : 0)}
                </Badge>
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="overflow-y-auto max-h-96">
          {/* Search Results */}
          {query.trim() === '' ? (
            <div className="space-y-6">
              {/* Search History */}
              {searchHistory.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-3 flex items-center space-x-2">
                    <History className="w-4 h-4" />
                    <span>Recent searches</span>
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {searchHistory.map((search, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => setQuery(search)}
                        className="text-xs"
                      >
                        <Clock className="w-3 h-3 mr-1" />
                        {search}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {/* Trending Searches */}
              <div>
                <h3 className="text-sm font-medium mb-3 flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4" />
                  <span>Trending searches</span>
                </h3>
                <div className="flex flex-wrap gap-2">
                  {trendingSearches.map((search, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setQuery(search)}
                      className="text-xs"
                    >
                      <TrendingUp className="w-3 h-3 mr-1" />
                      {search}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          ) : isLoading ? (
            <LoadingSkeleton type="user" count={5} />
          ) : results.length === 0 ? (
            <EmptyState
              icon={<Search className="w-8 h-8 text-gray-400" />}
              title="No results found"
              description={`No results found for "${query}". Try different keywords or adjust your filters.`}
            />
          ) : (
            <div className="space-y-3">
              {results.map((result) => {
                const IconComponent = getResultIcon(result.type);
                
                return (
                  <Card
                    key={result.id}
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    onClick={() => handleResultClick(result)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        {result.avatar ? (
                          <UserAvatar
                            user={{ id: result.id, name: result.title, avatar: result.avatar }}
                            size="md"
                          />
                        ) : result.thumbnail ? (
                          <img
                            src={result.thumbnail}
                            alt={result.title}
                            className="w-10 h-10 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="w-5 h-5 text-gray-400" />
                          </div>
                        )}
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-medium text-sm truncate">{result.title}</h3>
                            <Badge variant="secondary" className="text-xs">
                              {getResultTypeLabel(result.type)}
                            </Badge>
                            {result.metadata?.verified && (
                              <Badge variant="default" className="text-xs">Verified</Badge>
                            )}
                          </div>
                          
                          {result.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                              {result.description}
                            </p>
                          )}
                          
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            {result.timestamp && (
                              <span className="flex items-center space-x-1">
                                <Clock className="w-3 h-3" />
                                <span>{result.timestamp}</span>
                              </span>
                            )}
                            {result.location && (
                              <span className="flex items-center space-x-1">
                                <MapPin className="w-3 h-3" />
                                <span>{result.location}</span>
                              </span>
                            )}
                            {result.metadata?.likes && (
                              <span>{result.metadata.likes} likes</span>
                            )}
                            {result.metadata?.members && (
                              <span>{result.metadata.members.toLocaleString()} members</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedSearch;
